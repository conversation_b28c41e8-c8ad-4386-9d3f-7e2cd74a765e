import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import '../services/user_service.dart';
import '../theme.dart';
import '../utils/bubble_indicator_painter.dart';
import '../widgets/snackbar.dart';

class LoginPage extends StatefulWidget {
  final bool showSignUpTab;

  const LoginPage({super.key, this.showSignUpTab = false});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage>
    with SingleTickerProviderStateMixin {
  // 页面控制器
  late PageController _pageController;

  // 服务
  final UserService _userService = UserService();

  // 标签颜色
  Color left = Colors.black;
  Color right = Colors.white;

  // 登录相关控制器和状态
  final TextEditingController loginEmailController = TextEditingController();
  final TextEditingController loginPasswordController = TextEditingController();
  final FocusNode focusNodeEmail = FocusNode();
  final FocusNode focusNodePassword = FocusNode();
  bool _obscureTextPassword = true;
  bool _isLoading = false;
  String? _loginEmailError;
  String? _loginPasswordError;
  bool _agreedToTerms = true;

  // 注册相关控制器和状态
  final FocusNode focusNodePasswordSignUp = FocusNode();
  final FocusNode focusNodeConfirmPassword = FocusNode();
  final FocusNode focusNodeEmailSignUp = FocusNode();
  bool _obscureTextPasswordSignUp = true;
  bool _obscureTextConfirmPassword = true;
  bool _isLoadingSignUp = false;
  String? _signupEmailError;
  String? _signupPasswordError;
  String? _signupConfirmPasswordError;
  final TextEditingController signupEmailController = TextEditingController();
  final TextEditingController signupPasswordController =
      TextEditingController();
  final TextEditingController signupConfirmPasswordController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    _checkLoginStatus();

    // 根据showSignUpTab属性设置初始页面
    _pageController = PageController(initialPage: widget.showSignUpTab ? 1 : 0);

    // 设置初始标签颜色
    if (widget.showSignUpTab) {
      left = Colors.white;
      right = Colors.black;
    }
  }

  @override
  void dispose() {
    // 释放登录相关资源
    focusNodeEmail.dispose();
    focusNodePassword.dispose();
    loginEmailController.dispose();
    loginPasswordController.dispose();

    // 释放注册相关资源
    focusNodePasswordSignUp.dispose();
    focusNodeConfirmPassword.dispose();
    focusNodeEmailSignUp.dispose();
    signupEmailController.dispose();
    signupPasswordController.dispose();
    signupConfirmPasswordController.dispose();

    _pageController.dispose();
    super.dispose();
  }

  // 检查登录状态
  Future<void> _checkLoginStatus() async {
    // 如果已经登录，直接进入主页
    if (_userService.isLoggedIn()) {
      if (mounted) {
        // Navigator.pushReplacementNamed(context, '/main');
        Navigator.pop(context); // 跳转到上一页
      }
    }
  }

  void _onSignInButtonPress() {
    _pageController.animateToPage(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.decelerate,
    );
  }

  void _onSignUpButtonPress() {
    _pageController.animateToPage(
      1,
      duration: const Duration(milliseconds: 500),
      curve: Curves.decelerate,
    );
  }

  Widget _buildMenuBar(BuildContext context) {
    return Container(
      width: 300.0,
      height: 50.0,
      decoration: const BoxDecoration(
        color: Color(0x552B2B2B),
        borderRadius: BorderRadius.all(Radius.circular(25.0)),
      ),
      child: CustomPaint(
        painter: BubbleIndicatorPainter(pageController: _pageController),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: <Widget>[
            Expanded(
              child: TextButton(
                style: ButtonStyle(
                  overlayColor: WidgetStateProperty.all(Colors.transparent),
                ),
                onPressed: _onSignInButtonPress,
                child: Text(
                  '登录',
                  style: TextStyle(
                    color: left,
                    fontSize: 16.0,
                    fontFamily: 'WorkSansSemiBold',
                  ),
                ),
              ),
            ),
            Expanded(
              child: TextButton(
                style: ButtonStyle(
                  overlayColor: WidgetStateProperty.all(Colors.transparent),
                ),
                onPressed: _onSignUpButtonPress,
                child: Text(
                  '注册',
                  style: TextStyle(
                    color: right,
                    fontSize: 16.0,
                    fontFamily: 'WorkSansSemiBold',
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 邮箱登录/注册
  Future<void> _emailLogin() async {
    final email = loginEmailController.text.trim();
    final password = loginPasswordController.text.trim();

    // 清除之前的错误
    setState(() {
      _loginEmailError = null;
      _loginPasswordError = null;
    });

    if (email.isEmpty) {
      setState(() {
        _loginEmailError = '请输入邮箱';
      });
      return;
    }

    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      setState(() {
        _loginEmailError = '请输入有效的邮箱地址';
      });
      return;
    }

    if (password.isEmpty) {
      setState(() {
        _loginPasswordError = '请输入密码';
      });
      return;
    }

    if (!_agreedToTerms) {
      CustomSnackBar(
        context,
        const Text('请同意用户协议和隐私政策', style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.red,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // 使用邮箱登录，如果用户不存在则自动注册
      final user = await _userService.loginWithEmail(email, password);

      if (user != null && mounted) {
        CustomSnackBar(
          context,
          const Text('登录成功', style: TextStyle(color: Colors.white)),
          backgroundColor: Colors.green,
          snackBarAction: null,
        );
        Navigator.pushReplacementNamed(context, '/main');
      } else if (mounted) {
        setState(() {
          _loginPasswordError = '登录失败，请检查邮箱和密码';
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _loginPasswordError = '登录失败：${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 注册方法
  Future<void> _register() async {
    final email = signupEmailController.text.trim();
    final password = signupPasswordController.text.trim();
    final confirmPassword = signupConfirmPasswordController.text.trim();

    // 清除之前的错误
    setState(() {
      _signupEmailError = null;
      _signupPasswordError = null;
      _signupConfirmPasswordError = null;
    });

    // 验证输入
    if (email.isEmpty) {
      setState(() {
        _signupEmailError = '请输入邮箱';
      });
      return;
    }

    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email)) {
      setState(() {
        _signupEmailError = '请输入有效的邮箱地址';
      });
      return;
    }

    if (password.isEmpty) {
      setState(() {
        _signupPasswordError = '请输入密码';
      });
      return;
    }

    if (password.length < 6) {
      setState(() {
        _signupPasswordError = '密码长度不能少于6位';
      });
      return;
    }

    if (confirmPassword.isEmpty) {
      setState(() {
        _signupConfirmPasswordError = '请确认密码';
      });
      return;
    }

    if (password != confirmPassword) {
      setState(() {
        _signupConfirmPasswordError = '两次输入的密码不一致';
      });
      return;
    }

    setState(() {
      _isLoadingSignUp = true;
    });

    try {
      // 使用邮箱和密码登录，如果不存在会自动注册
      final user = await _userService.loginWithEmail(email, password);

      if (user != null && mounted) {
        CustomSnackBar(
          context,
          const Text('注册成功！', style: TextStyle(color: Colors.white)),
          backgroundColor: Colors.green,
        );
        Navigator.pushReplacementNamed(context, '/main');
      } else {
        throw Exception('注册失败，请稍后重试');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _signupPasswordError = '注册失败：${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingSignUp = false;
        });
      }
    }
  }

  // 切换密码可见性
  void _toggleLogin() {
    setState(() {
      _obscureTextPassword = !_obscureTextPassword;
    });
  }

  // 切换注册密码可见性
  void _toggleSignup() {
    setState(() {
      _obscureTextPasswordSignUp = !_obscureTextPasswordSignUp;
    });
  }

  // 切换确认密码可见性
  void _toggleSignupConfirm() {
    setState(() {
      _obscureTextConfirmPassword = !_obscureTextConfirmPassword;
    });
  }

  // 构建登录视图
  Widget _buildSignInView() {
    return Container(
      padding: const EdgeInsets.only(top: 23.0),
      child: Column(
        children: <Widget>[
          Stack(
            alignment: Alignment.topCenter,
            children: <Widget>[
              Card(
                elevation: 2.0,
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: SizedBox(
                  width: 300.0,
                  height: 190.0,
                  child: Column(
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.only(
                          top: 20.0,
                          bottom: 20.0,
                          left: 25.0,
                          right: 25.0,
                        ),
                        child: TextField(
                          focusNode: focusNodeEmail,
                          controller: loginEmailController,
                          keyboardType: TextInputType.emailAddress,
                          style: const TextStyle(
                            fontFamily: 'WorkSansSemiBold',
                            fontSize: 16.0,
                            color: Colors.black,
                          ),
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            icon: Icon(
                              FontAwesomeIcons.envelope,
                              color:
                                  _loginEmailError != null
                                      ? Colors.red
                                      : Colors.black,
                              size: 22.0,
                            ),
                            hintText: _loginEmailError ?? '邮箱地址',
                            hintStyle: TextStyle(
                              fontFamily: 'WorkSansSemiBold',
                              fontSize: 17.0,
                              color:
                                  _loginEmailError != null
                                      ? Colors.red
                                      : Colors.grey,
                            ),
                          ),
                          onSubmitted: (_) {
                            focusNodePassword.requestFocus();
                          },
                        ),
                      ),
                      Container(
                        width: 250.0,
                        height: 1.0,
                        color: Colors.grey[400],
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                          top: 20.0,
                          bottom: 20.0,
                          left: 25.0,
                          right: 25.0,
                        ),
                        child: TextField(
                          focusNode: focusNodePassword,
                          controller: loginPasswordController,
                          obscureText: _obscureTextPassword,
                          style: const TextStyle(
                            fontFamily: 'WorkSansSemiBold',
                            fontSize: 16.0,
                            color: Colors.black,
                          ),
                          decoration: InputDecoration(
                            border: InputBorder.none,
                            icon: Icon(
                              FontAwesomeIcons.lock,
                              size: 22.0,
                              color:
                                  _loginPasswordError != null
                                      ? Colors.red
                                      : Colors.black,
                            ),
                            hintText: _loginPasswordError ?? '密码',
                            hintStyle: TextStyle(
                              fontFamily: 'WorkSansSemiBold',
                              fontSize: 17.0,
                              color:
                                  _loginPasswordError != null
                                      ? Colors.red
                                      : Colors.grey,
                            ),
                            suffixIcon: GestureDetector(
                              onTap: _toggleLogin,
                              child: Icon(
                                _obscureTextPassword
                                    ? FontAwesomeIcons.eye
                                    : FontAwesomeIcons.eyeSlash,
                                size: 15.0,
                                color: Colors.black,
                              ),
                            ),
                          ),
                          onSubmitted: (_) {
                            _emailLogin();
                          },
                          textInputAction: TextInputAction.go,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 170.0),
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(5.0)),
                  boxShadow: CustomTheme.buttonShadow,
                  gradient: CustomTheme.buttonGradient,
                ),
                child: MaterialButton(
                  highlightColor: Colors.transparent,
                  splashColor: CustomTheme.loginGradientEnd,
                  onPressed: _isLoading ? null : _emailLogin,
                  child:
                      _isLoading
                          ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                          : const Padding(
                            padding: EdgeInsets.symmetric(
                              vertical: 10.0,
                              horizontal: 42.0,
                            ),
                            child: Text(
                              '登录',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 25.0,
                                fontFamily: 'WorkSansBold',
                              ),
                            ),
                          ),
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(top: 10.0),
            child: TextButton(
              onPressed: () {},
              child: const Text(
                '忘记密码?',
                style: TextStyle(
                  decoration: TextDecoration.underline,
                  color: Colors.white,
                  fontSize: 16.0,
                  fontFamily: 'WorkSansMedium',
                ),
              ),
            ),
          ),

          Padding(
            padding: const EdgeInsets.only(top: 10.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: <Color>[Colors.white10, Colors.white],
                      begin: FractionalOffset(0.0, 0.0),
                      end: FractionalOffset(1.0, 1.0),
                      stops: <double>[0.0, 1.0],
                      tileMode: TileMode.clamp,
                    ),
                  ),
                  width: 100.0,
                  height: 1.0,
                ),
                const Padding(
                  padding: EdgeInsets.only(left: 15.0, right: 15.0),
                  child: Text(
                    '或',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16.0,
                      fontFamily: 'WorkSansMedium',
                    ),
                  ),
                ),
                Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: <Color>[Colors.white, Colors.white10],
                      begin: FractionalOffset(0.0, 0.0),
                      end: FractionalOffset(1.0, 1.0),
                      stops: <double>[0.0, 1.0],
                      tileMode: TileMode.clamp,
                    ),
                  ),
                  width: 100.0,
                  height: 1.0,
                ),
              ],
            ),
          ),
          // 用户协议
          Padding(
            padding: const EdgeInsets.only(top: 10.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Transform.scale(
                  scale: 0.8,
                  child: Checkbox(
                    value: _agreedToTerms,
                    activeColor: CustomTheme.loginGradientEnd,
                    onChanged: (value) {
                      setState(() {
                        _agreedToTerms = value ?? true;
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RichText(
                    text: const TextSpan(
                      style: TextStyle(color: Colors.white, fontSize: 12),
                      children: [
                        TextSpan(text: '已阅读并同意《用户协议》、'),
                        TextSpan(text: '《隐私政策》、'),
                        TextSpan(text: '《内容平台加入协议》'),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建注册视图
  Widget _buildSignUpView() {
    return Container(
      padding: const EdgeInsets.only(top: 23.0),
      child: Column(
        children: <Widget>[
          Stack(
            alignment: Alignment.topCenter,
            children: <Widget>[
              Card(
                elevation: 2.0,
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: SizedBox(
                  width: 300.0,
                  height: 270.0, // 修改后的卡片高度，原来是400.0
                  child: SingleChildScrollView(
                    child: Column(
                      children: <Widget>[
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 20.0,
                            bottom: 20.0,
                            left: 25.0,
                            right: 25.0,
                          ),
                          child: TextField(
                            focusNode: focusNodeEmailSignUp,
                            controller: signupEmailController,
                            keyboardType: TextInputType.emailAddress,
                            autocorrect: false,
                            style: const TextStyle(
                              fontFamily: 'WorkSansSemiBold',
                              fontSize: 16.0,
                              color: Colors.black,
                            ),
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              icon: Icon(
                                FontAwesomeIcons.envelope,
                                color:
                                    _signupEmailError != null
                                        ? Colors.red
                                        : Colors.black,
                              ),
                              hintText: _signupEmailError ?? '邮箱地址',
                              hintStyle: TextStyle(
                                fontFamily: 'WorkSansSemiBold',
                                fontSize: 16.0,
                                color:
                                    _signupEmailError != null
                                        ? Colors.red
                                        : Colors.grey,
                              ),
                            ),
                            onSubmitted: (_) {
                              focusNodePasswordSignUp.requestFocus();
                            },
                          ),
                        ),
                        Container(
                          width: 250.0,
                          height: 1.0,
                          color: Colors.grey[400],
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 20.0,
                            bottom: 20.0,
                            left: 25.0,
                            right: 25.0,
                          ),
                          child: TextField(
                            focusNode: focusNodePasswordSignUp,
                            controller: signupPasswordController,
                            obscureText: _obscureTextPasswordSignUp,
                            autocorrect: false,
                            style: const TextStyle(
                              fontFamily: 'WorkSansSemiBold',
                              fontSize: 16.0,
                              color: Colors.black,
                            ),
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              icon: Icon(
                                FontAwesomeIcons.lock,
                                color:
                                    _signupPasswordError != null
                                        ? Colors.red
                                        : Colors.black,
                              ),
                              hintText: _signupPasswordError ?? '密码',
                              hintStyle: TextStyle(
                                fontFamily: 'WorkSansSemiBold',
                                fontSize: 16.0,
                                color:
                                    _signupPasswordError != null
                                        ? Colors.red
                                        : Colors.grey,
                              ),
                              suffixIcon: GestureDetector(
                                onTap: _toggleSignup,
                                child: Icon(
                                  _obscureTextPasswordSignUp
                                      ? FontAwesomeIcons.eye
                                      : FontAwesomeIcons.eyeSlash,
                                  size: 15.0,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                            onSubmitted: (_) {
                              focusNodeConfirmPassword.requestFocus();
                            },
                          ),
                        ),
                        Container(
                          width: 250.0,
                          height: 1.0,
                          color: Colors.grey[400],
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 20.0,
                            bottom: 20.0,
                            left: 25.0,
                            right: 25.0,
                          ),
                          child: TextField(
                            focusNode: focusNodeConfirmPassword,
                            controller: signupConfirmPasswordController,
                            obscureText: _obscureTextConfirmPassword,
                            autocorrect: false,
                            style: const TextStyle(
                              fontFamily: 'WorkSansSemiBold',
                              fontSize: 16.0,
                              color: Colors.black,
                            ),
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              icon: Icon(
                                FontAwesomeIcons.lock,
                                color:
                                    _signupConfirmPasswordError != null
                                        ? Colors.red
                                        : Colors.black,
                              ),
                              hintText: _signupConfirmPasswordError ?? '确认密码',
                              hintStyle: TextStyle(
                                fontFamily: 'WorkSansSemiBold',
                                fontSize: 16.0,
                                color:
                                    _signupConfirmPasswordError != null
                                        ? Colors.red
                                        : Colors.grey,
                              ),
                              suffixIcon: GestureDetector(
                                onTap: _toggleSignupConfirm,
                                child: Icon(
                                  _obscureTextConfirmPassword
                                      ? FontAwesomeIcons.eye
                                      : FontAwesomeIcons.eyeSlash,
                                  size: 15.0,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                            onSubmitted: (_) {
                              _register();
                            },
                            textInputAction: TextInputAction.go,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Container(
                margin: const EdgeInsets.only(top: 250.0), // 修改按钮位置，原来是380.0
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(5.0)),
                  boxShadow: CustomTheme.buttonShadow,
                  gradient: CustomTheme.buttonGradient,
                ),
                child: MaterialButton(
                  highlightColor: Colors.transparent,
                  splashColor: CustomTheme.loginGradientEnd,
                  onPressed: _isLoadingSignUp ? null : _register,
                  child:
                      _isLoadingSignUp
                          ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          )
                          : const Padding(
                            padding: EdgeInsets.symmetric(
                              vertical: 10.0,
                              horizontal: 42.0,
                            ),
                            child: Text(
                              '注册',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 25.0,
                                fontFamily: 'WorkSansBold',
                              ),
                            ),
                          ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        physics: const ClampingScrollPhysics(),
        child: GestureDetector(
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: <Color>[
                  Color.fromARGB(255, 255, 255, 255),
                  Color.fromARGB(255, 65, 150, 247),
                ],
                begin: FractionalOffset(0.0, 0.0),
                end: FractionalOffset(1.0, 1.0),
                stops: <double>[0.0, 1.0],
                tileMode: TileMode.clamp,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(top: 75.0),
                  child: Image.asset(
                    'assets/images/logo.png',
                    height:
                        MediaQuery.of(context).size.height > 800 ? 191.0 : 150,
                    fit: BoxFit.fill,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 20.0),
                  child: _buildMenuBar(context),
                ),
                Expanded(
                  flex: 2,
                  child: PageView(
                    controller: _pageController,
                    physics: const ClampingScrollPhysics(),
                    onPageChanged: (int i) {
                      FocusScope.of(context).requestFocus(FocusNode());
                      if (i == 0) {
                        setState(() {
                          right = Colors.white;
                          left = Colors.black;
                        });
                      } else if (i == 1) {
                        setState(() {
                          right = Colors.black;
                          left = Colors.white;
                        });
                      }
                    },
                    children: <Widget>[
                      ConstrainedBox(
                        constraints: const BoxConstraints.expand(),
                        child: _buildSignInView(),
                      ),
                      ConstrainedBox(
                        constraints: const BoxConstraints.expand(),
                        child: _buildSignUpView(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
