import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:geolocator/geolocator.dart';

/// 测试修复效果的脚本
void main() {
  group('后端连接问题修复测试', () {
    test('Supabase 连接测试', () async {
      try {
        // 测试 Supabase 初始化
        await Supabase.initialize(
          url: "https://vqeqhvwrfdvpuocgnrxt.supabase.co",
          anonKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZxZXFodndyZmR2cHVvY2ducnh0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcxODQyMDAsImV4cCI6MjA2Mjc2MDIwMH0.WVqww6KdARR-npPf-jxXS0-_pvLW76EeMVCWV29wMQQ",
          debug: true,
        );
        
        final client = Supabase.instance.client;
        
        // 测试简单的查询
        final response = await client.from('users').select('id').limit(1);
        
        print('Supabase 连接成功: ${response.length} 条记录');
        expect(response, isNotNull);
      } catch (e) {
        print('Supabase 连接失败: $e');
        fail('Supabase 连接测试失败: $e');
      }
    });

    test('地理位置服务测试', () async {
      try {
        // 测试位置服务是否可用
        bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
        print('位置服务状态: ${serviceEnabled ? "启用" : "未启用"}');
        
        // 测试权限检查
        LocationPermission permission = await Geolocator.checkPermission();
        print('位置权限状态: $permission');
        
        expect(serviceEnabled, isNotNull);
        expect(permission, isNotNull);
      } catch (e) {
        print('地理位置服务测试失败: $e');
        // 不让测试失败，因为在测试环境中可能没有位置服务
      }
    });
  });
}
