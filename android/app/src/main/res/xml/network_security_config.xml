<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="false">
        <domain includeSubdomains="true">supabase.co</domain>
        <domain includeSubdomains="true">vqeqhvwrfdvpuocgnrxt.supabase.co</domain>
        <domain includeSubdomains="true">tianditu.gov.cn</domain>
        <trust-anchors>
            <!-- Trust preinstalled CAs -->
            <certificates src="system"/>
            <!-- Additionally trust user added CAs -->
            <certificates src="user"/>
        </trust-anchors>
    </domain-config>
    
    <!-- Allow cleartext traffic for localhost (development) -->
    <domain-config cleartextTrafficPermitted="true">
        <domain includeSubdomains="true">localhost</domain>
        <domain includeSubdomains="true">127.0.0.1</domain>
        <domain includeSubdomains="true">********</domain>
    </domain-config>
</network-security-config>
